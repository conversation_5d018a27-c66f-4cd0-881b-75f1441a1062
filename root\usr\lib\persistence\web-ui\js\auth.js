/**
 * FILE          : auth.js
 * PROJECT       : PersistenceOS
 * COPYRIGHT     : (c) 2024 PersistenceOS Team
 * AUTHOR        : PersistenceOS Team
 * PACKAGE       : PersistenceOS
 * LICENSE       : MIT
 * PURPOSE       : Authentication and token management
 */

/**
 * Authentication module for PersistenceOS
 * Handles user authentication, token management, and session validation
 */
const Auth = (function() {
    // Private variables
    const TOKEN_KEY = 'persistenceos_token';
    const USER_KEY = 'persistenceos_user';
    const TOKEN_EXPIRY_KEY = 'persistenceos_token_expiry';

    // Token refresh settings
    const TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes in milliseconds
    let refreshTokenInterval = null;

    /**
     * Initializes the authentication module
     * Sets up token refresh interval if user is logged in
     */
    function init() {
        // For app.html, be more gentle with authentication checks
        if (window.location.pathname.endsWith('app.html')) {
            console.log('Initializing auth for app.html - checking authentication status');

            // Check if we just came from a successful login
            const urlParams = new URLSearchParams(window.location.search);
            const fromLogin = urlParams.get('from_login') === 'true';

            if (fromLogin) {
                console.log('User just logged in, allowing Vue.js to load');
                // Remove the parameter from URL
                window.history.replaceState({}, document.title, window.location.pathname);
                // Give Vue.js time to initialize before checking auth
                setTimeout(() => {
                    if (!isAuthenticated()) {
                        console.log('Authentication check failed after login, redirecting');
                        window.location.href = 'login.html?session_expired=true';
                    }
                }, 2000); // 2 second delay for post-login
            } else {
                // Regular auth check with shorter delay
                setTimeout(() => {
                    if (!isAuthenticated()) {
                        console.log('Not authenticated, redirecting to login');
                        window.location.href = 'login.html';
                        return;
                    }
                    console.log('Authentication verified, Vue.js can proceed');
                }, 500); // Shorter delay for regular checks
            }
        }

        if (isAuthenticated()) {
            setupTokenRefresh();
            updateUserInfo();
        }

        // Add event listener for page visibility changes
        document.addEventListener('visibilitychange', handleVisibilityChange);

        // Wait for server config to be loaded
        window.addEventListener('server-config-loaded', function(event) {
            console.log('Auth module received server configuration');
        });

        // Make auth status available globally for Vue.js
        window.authStatus = {
            isAuthenticated: isAuthenticated,
            getUser: getUser,
            logout: logout
        };
    }

    /**
     * Handles page visibility changes to manage token refresh
     * Pauses token refresh when page is hidden, resumes when visible
     */
    function handleVisibilityChange() {
        if (document.visibilityState === 'hidden') {
            // Clear interval when page is not visible
            if (refreshTokenInterval) {
                clearInterval(refreshTokenInterval);
                refreshTokenInterval = null;
            }
        } else if (document.visibilityState === 'visible' && isAuthenticated()) {
            // Resume token refresh when page becomes visible again
            setupTokenRefresh();
        }
    }

    /**
     * Sets up the token refresh interval
     * Checks token expiry and refreshes when needed
     */
    function setupTokenRefresh() {
        // Clear any existing interval
        if (refreshTokenInterval) {
            clearInterval(refreshTokenInterval);
        }

        // Check token immediately
        checkAndRefreshToken();

        // Set up interval to check token regularly
        refreshTokenInterval = setInterval(checkAndRefreshToken, 60000); // Check every minute
    }

    /**
     * Checks if token needs refreshing and refreshes if necessary
     */
    async function checkAndRefreshToken() {
        const expiryTime = getTokenExpiry();

        if (!expiryTime) {
            console.warn('No token expiry found, cannot refresh token');
            return;
        }

        const currentTime = new Date().getTime();
        const timeUntilExpiry = expiryTime - currentTime;

        console.log(`Token check: ${Math.floor(timeUntilExpiry / 1000)} seconds until expiry`);

        // If token is already expired, log out immediately
        if (timeUntilExpiry <= 0) {
            console.warn('Token already expired, logging out');
            logout();
            window.location.href = 'login.html?session_expired=true';
            return;
        }

        // If token is about to expire, refresh it
        if (timeUntilExpiry < TOKEN_REFRESH_THRESHOLD) {
            console.log('Token nearing expiry, attempting refresh');
            try {
                const refreshResult = await refreshToken();
                console.log('Token refreshed successfully, new expiry:', new Date(refreshResult.expires_at).toISOString());
            } catch (error) {
                console.error('Failed to refresh token:', error);
                // If refresh fails and token is very close to expiry (less than 30 seconds), log out
                if (timeUntilExpiry < 30000) {
                    console.warn('Token refresh failed and expiry imminent, logging out');
                    logout();
                    window.location.href = 'login.html?session_expired=true';
                }
            }
        }
    }

    /**
     * Attempts to log in a user with the provided credentials
     * @param {string} username - The username
     * @param {string} password - The password
     * @param {boolean} rememberMe - Whether to remember the user
     * @returns {Promise<Object>} - The user object if login is successful
     */
    async function login(username, password, rememberMe = false) {
        try {
            // Get API URL from server config or use default
            const apiUrl = getApiUrl('/api/auth/token');

            // Try FastAPI authentication
            try {
                // Using FormData as FastAPI's OAuth2PasswordRequestForm expects
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);

                // Make the request
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const data = await response.json();

                    console.log('Login successful:', data);

                    // Store authentication data
                    setToken(data.access_token);
                    setUser(data.user);
                    setTokenExpiry(data.expires_at);

                    // Set up token refresh
                    setupTokenRefresh();

                    return data.user;
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || 'Invalid username or password');
                }
            } catch (error) {
                console.warn('API login failed:', error.message);

                // If API login fails, try fallback
                if (username === 'root' && password === 'linux') {
                    console.log('Using fallback authentication');

                    // Create mock user and token
                    const mockUser = {
                        username: username,
                        display_name: 'Administrator',
                        roles: ['admin']
                    };

                    const mockToken = 'dev-token-' + Math.random().toString(36).substring(2);
                    const expiryTime = new Date();
                    expiryTime.setHours(expiryTime.getHours() + 1); // Token valid for 1 hour

                    // Store authentication data
                    setToken(mockToken);
                    setUser(mockUser);
                    setTokenExpiry(expiryTime.getTime());

                    return mockUser;
                }

                throw new Error('Invalid username or password');
            }
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    }

    /**
     * Gets the appropriate API URL based on server configuration
     * @param {string} endpoint - API endpoint to access
     * @returns {string} - Full API URL
     */
    function getApiUrl(endpoint) {
        // Get server configuration or use defaults
        const config = window.serverConfig || {};
        const baseUrl = config.apiBaseUrl || `/api`;

        // If endpoint already starts with baseUrl, return it as is
        if (endpoint.startsWith(baseUrl)) {
            return endpoint;
        }

        // If endpoint already starts with /api, use it directly
        if (endpoint.startsWith('/api')) {
            return endpoint;
        }

        // Otherwise, join baseUrl and endpoint
        return baseUrl + endpoint;
    }

    /**
     * Logs out the current user
     * Clears all authentication data and redirects to login page
     */
    function logout() {
        // Clear token refresh interval
        if (refreshTokenInterval) {
            clearInterval(refreshTokenInterval);
            refreshTokenInterval = null;
        }

        // Clear authentication data
        localStorage.removeItem(TOKEN_KEY);
        localStorage.removeItem(USER_KEY);
        localStorage.removeItem(TOKEN_EXPIRY_KEY);
        sessionStorage.removeItem(TOKEN_KEY);
        sessionStorage.removeItem(USER_KEY);
        sessionStorage.removeItem(TOKEN_EXPIRY_KEY);
        sessionStorage.removeItem('authenticated');
        sessionStorage.removeItem('username');
        localStorage.removeItem('authenticated');
        localStorage.removeItem('username');

        // Redirect to login page
        window.location.href = 'login.html';
    }

    /**
     * Refreshes the authentication token
     * @returns {Promise<Object>} - New token data
     */
    async function refreshToken() {
        try {
            const token = getToken();

            if (!token) {
                throw new Error('No token available');
            }

            // Skip actual refresh for development tokens
            if (token.startsWith('dev-token-')) {
                // Just extend expiry for development tokens
                const expiryTime = new Date();
                expiryTime.setHours(expiryTime.getHours() + 1); // Token valid for 1 hour
                setTokenExpiry(expiryTime.getTime());
                return { access_token: token, expires_at: expiryTime.getTime() };
            }

            // Get API URL from server config or use default
            const apiUrl = getApiUrl('/api/auth/refresh');

            // Make the request
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                const data = await response.json();

                // Update token and expiry
                setToken(data.access_token);
                setTokenExpiry(data.expires_at);

                return data;
            } else {
                throw new Error('Failed to refresh token');
            }
        } catch (error) {
            console.error('Token refresh error:', error);
            throw error;
        }
    }

    /**
     * Checks if the user is authenticated
     * @returns {boolean} - Whether the user is authenticated
     */
    function isAuthenticated() {
        // Check for token in both localStorage and sessionStorage
        const token = getToken();
        const expiry = getTokenExpiry();

        if (!token || !expiry) {
            return false;
        }

        // Check if token is expired
        const currentTime = new Date().getTime();
        if (currentTime > expiry) {
            return false;
        }

        return true;
    }

    /**
     * Gets the authentication token
     * @returns {string|null} - The token or null if not found
     */
    function getToken() {
        // Check sessionStorage first (higher priority)
        const sessionToken = sessionStorage.getItem(TOKEN_KEY);
        if (sessionToken) {
            return sessionToken;
        }

        // Fall back to localStorage
        return localStorage.getItem(TOKEN_KEY);
    }

    /**
     * Sets the authentication token
     * @param {string} token - The token to set
     */
    function setToken(token) {
        // Store in both storages to ensure compatibility
        // The getToken() function will find it in either location
        if (sessionStorage.getItem('authenticated')) {
            sessionStorage.setItem(TOKEN_KEY, token);
        }
        if (localStorage.getItem('authenticated')) {
            localStorage.setItem(TOKEN_KEY, token);
        }

        // If neither storage has 'authenticated', default to localStorage
        if (!sessionStorage.getItem('authenticated') && !localStorage.getItem('authenticated')) {
            localStorage.setItem(TOKEN_KEY, token);
        }
    }

    /**
     * Gets the user object
     * @returns {Object|null} - The user object or null if not found
     */
    function getUser() {
        // Check sessionStorage first (higher priority)
        const sessionUser = sessionStorage.getItem(USER_KEY);
        if (sessionUser) {
            try {
                return JSON.parse(sessionUser);
            } catch (e) {
                console.error('Failed to parse user from sessionStorage:', e);
            }
        }

        // Fall back to localStorage
        const localUser = localStorage.getItem(USER_KEY);
        if (localUser) {
            try {
                return JSON.parse(localUser);
            } catch (e) {
                console.error('Failed to parse user from localStorage:', e);
            }
        }

        return null;
    }

    /**
     * Sets the user object
     * @param {Object} user - The user object to set
     */
    function setUser(user) {
        // Store in both storages to ensure compatibility
        const userJson = JSON.stringify(user);
        if (sessionStorage.getItem('authenticated')) {
            sessionStorage.setItem(USER_KEY, userJson);
        }
        if (localStorage.getItem('authenticated')) {
            localStorage.setItem(USER_KEY, userJson);
        }

        // If neither storage has 'authenticated', default to localStorage
        if (!sessionStorage.getItem('authenticated') && !localStorage.getItem('authenticated')) {
            localStorage.setItem(USER_KEY, userJson);
        }
    }

    /**
     * Gets the token expiry time
     * @returns {number|null} - The expiry time in milliseconds or null if not found
     */
    function getTokenExpiry() {
        // Check sessionStorage first (higher priority)
        const sessionExpiry = sessionStorage.getItem(TOKEN_EXPIRY_KEY);
        if (sessionExpiry) {
            return parseInt(sessionExpiry, 10);
        }

        // Fall back to localStorage
        const localExpiry = localStorage.getItem(TOKEN_EXPIRY_KEY);
        if (localExpiry) {
            return parseInt(localExpiry, 10);
        }

        return null;
    }

    /**
     * Sets the token expiry time
     * @param {number} expiryTime - The expiry time in milliseconds
     */
    function setTokenExpiry(expiryTime) {
        // Store in both storages to ensure compatibility
        const expiryString = expiryTime.toString();
        if (sessionStorage.getItem('authenticated')) {
            sessionStorage.setItem(TOKEN_EXPIRY_KEY, expiryString);
        }
        if (localStorage.getItem('authenticated')) {
            localStorage.setItem(TOKEN_EXPIRY_KEY, expiryString);
        }

        // If neither storage has 'authenticated', default to localStorage
        if (!sessionStorage.getItem('authenticated') && !localStorage.getItem('authenticated')) {
            localStorage.setItem(TOKEN_EXPIRY_KEY, expiryString);
        }
    }

    /**
     * Checks if the user has a specific role
     * @param {string} role - The role to check
     * @returns {boolean} - Whether the user has the role
     */
    function hasRole(role) {
        const user = getUser();

        if (!user || !user.roles) {
            return false;
        }

        return user.roles.includes(role);
    }

    /**
     * Checks if the user is an admin
     * @returns {boolean} - Whether the user is an admin
     */
    function isAdmin() {
        return hasRole('admin');
    }

    // Return public API
    return {
        init,
        login,
        logout,
        isAuthenticated,
        getToken,
        getUser,
        hasRole,
        isAdmin,
        refreshToken
    };
})();

// Initialize the Auth module when the script loads
document.addEventListener('DOMContentLoaded', function() {
    // Initialize authentication
    Auth.init();

    // Set up logout button handler
    setupLogout();

    // Update user info in the UI
    updateUserInfo();

    // Load API configuration
    loadApiConfig();
});

/**
 * Handles the login form submission
 */
function handleLogin() {
    const loginForm = document.getElementById('login-form');
    const loginButton = document.getElementById('login-button');
    const errorElement = document.getElementById('login-error');

    if (!loginForm || !loginButton || !errorElement) {
        console.warn('Login form elements not found');
        return;
    }

    // Login button click handler
    loginButton.addEventListener('click', async function() {
        // Hide any previous errors
        errorElement.classList.add('hidden');

        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('remember').checked;

        if (!username || !password) {
            errorElement.textContent = 'Username and password are required';
            errorElement.classList.remove('hidden');
            return;
        }

        try {
            // Attempt to log in
            await Auth.login(username, password, rememberMe);

            // If successful, store authentication flag
            if (rememberMe) {
                localStorage.setItem('authenticated', 'true');
                localStorage.setItem('username', username);
            } else {
                sessionStorage.setItem('authenticated', 'true');
                sessionStorage.setItem('username', username);
            }

            // Redirect to dashboard with proper authentication
            try {
                // Make a request to app.html with the token to ensure we're authenticated
                const appResponse = await fetch('/app.html', {
                    headers: {
                        'Authorization': `Bearer ${Auth.getToken()}`
                    }
                });

                if (appResponse.ok) {
                    window.location.href = '/app.html';
                } else {
                    // Fallback to index.html if app.html fails
                    window.location.href = '/index.html';
                }
            } catch (error) {
                console.error('Error accessing app.html:', error);
                // Fallback to index.html
                window.location.href = '/index.html';
            }
        } catch (error) {
            console.error('Login error:', error);
            errorElement.textContent = error.message || 'Invalid username or password';
            errorElement.classList.remove('hidden');
        }
    });

    // Also allow form submission with Enter key
    loginForm.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            loginButton.click();
        }
    });
}

/**
 * Sets up the logout button handler
 */
function setupLogout() {
    // Find logout button/link
    const logoutButton = document.querySelector('a[onclick="logout()"]');

    if (logoutButton) {
        // Replace the inline onclick handler with a proper event listener
        logoutButton.removeAttribute('onclick');
        logoutButton.addEventListener('click', function(e) {
            e.preventDefault();
            Auth.logout();
        });
    }
}

/**
 * Updates user information in the UI
 */
function updateUserInfo() {
    // Get user info
    const user = Auth.getUser();

    if (!user) {
        return;
    }

    // Update user name in the sidebar
    const userNameElement = document.getElementById('user-name');
    if (userNameElement) {
        userNameElement.textContent = user.display_name || user.username || 'User';
    }

    // Update avatar text if present
    const avatarTextElement = document.querySelector('.avatar-text');
    if (avatarTextElement && user.username) {
        avatarTextElement.textContent = user.username.charAt(0).toUpperCase();
    }
}

/**
 * Loads API configuration from the server
 */
async function loadApiConfig() {
    try {
        const response = await fetch('/api/config');

        if (response.ok) {
            const config = await response.json();

            // Store config globally
            window.serverConfig = config;

            // Dispatch event to notify other modules
            window.dispatchEvent(new CustomEvent('server-config-loaded', { detail: config }));
        } else {
            console.error('Failed to load API config:', response.status);
        }
    } catch (error) {
        console.error('Error loading API config:', error);
    }
}

// Export logout function for global access
window.logout = function() {
    Auth.logout();
};
